<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Course;
use App\Models\User;

echo "=== FEATURED TOGGLE DEBUG SCRIPT ===\n\n";

// 1. Check if courses exist
echo "1. COURSE AVAILABILITY CHECK:\n";
$courses = Course::all();
echo "Total courses: " . $courses->count() . "\n";

if ($courses->count() === 0) {
    echo "ERROR: No courses found in database!\n";
    exit(1);
}

$testCourse = $courses->first();
echo "Test course: {$testCourse->title}\n";
echo "Course ID: {$testCourse->id}\n";
echo "Course Slug: {$testCourse->slug}\n";
echo "Current Featured Status: " . ($testCourse->featured ? 'true' : 'false') . "\n\n";

// 2. Check route model binding
echo "2. ROUTE MODEL BINDING TEST:\n";
echo "Route Key Name: " . $testCourse->getRouteKeyName() . "\n";
echo "Route Key Value: " . $testCourse->getRouteKey() . "\n";

// Test resolution by slug
$resolvedBySlug = Course::where('slug', $testCourse->slug)->first();
echo "Resolution by slug: " . ($resolvedBySlug ? 'SUCCESS' : 'FAILED') . "\n";

// Test resolution by ID
$resolvedById = Course::find($testCourse->id);
echo "Resolution by ID: " . ($resolvedById ? 'SUCCESS' : 'FAILED') . "\n\n";

// 3. Check instructor permissions
echo "3. INSTRUCTOR PERMISSION CHECK:\n";
$instructor = User::where('role', 'instructor')->first();
if ($instructor) {
    echo "Test instructor: {$instructor->name} ({$instructor->email})\n";
    echo "Instructor ID: {$instructor->id}\n";
    
    // Check if instructor owns the test course
    $instructorCourse = Course::where('instructor_id', $instructor->id)->first();
    if ($instructorCourse) {
        echo "Instructor course found: {$instructorCourse->title}\n";
        echo "Course ID: {$instructorCourse->id}\n";
        echo "Course Slug: {$instructorCourse->slug}\n";
        echo "Featured Status: " . ($instructorCourse->featured ? 'true' : 'false') . "\n";
        $testCourse = $instructorCourse; // Use instructor's course for testing
    } else {
        echo "WARNING: No courses found for this instructor\n";
    }
} else {
    echo "ERROR: No instructor found in database!\n";
}
echo "\n";

// 4. Test database update directly
echo "4. DIRECT DATABASE UPDATE TEST:\n";
$originalFeatured = $testCourse->featured;
echo "Original featured status: " . ($originalFeatured ? 'true' : 'false') . "\n";

try {
    // Toggle featured status
    $testCourse->update(['featured' => !$testCourse->featured]);
    $testCourse->refresh();
    
    echo "After toggle: " . ($testCourse->featured ? 'true' : 'false') . "\n";
    echo "Database update: SUCCESS\n";
    
    // Toggle back to original state
    $testCourse->update(['featured' => $originalFeatured]);
    $testCourse->refresh();
    echo "Restored to original: " . ($testCourse->featured ? 'true' : 'false') . "\n";
    
} catch (\Exception $e) {
    echo "Database update: FAILED - " . $e->getMessage() . "\n";
}
echo "\n";

// 5. Test URL generation
echo "5. URL GENERATION TEST:\n";
try {
    $featuredUrl = route('instructor.courses.toggle-featured', $testCourse);
    echo "Featured toggle URL: {$featuredUrl}\n";
    
    $statusUrl = route('instructor.courses.toggle-status', $testCourse);
    echo "Status toggle URL: {$statusUrl}\n";
    
    $publishUrl = route('instructor.course-builder.toggle-publish', $testCourse);
    echo "Publish toggle URL: {$publishUrl}\n";
    
} catch (\Exception $e) {
    echo "URL generation: FAILED - " . $e->getMessage() . "\n";
}
echo "\n";

// 6. Check route definitions
echo "6. ROUTE DEFINITION CHECK:\n";
$router = app('router');
$routes = $router->getRoutes();

$featuredRoute = $routes->getByName('instructor.courses.toggle-featured');
$statusRoute = $routes->getByName('instructor.courses.toggle-status');
$publishRoute = $routes->getByName('instructor.course-builder.toggle-publish');

if ($featuredRoute) {
    echo "Featured route found: " . $featuredRoute->uri() . " [" . implode(',', $featuredRoute->methods()) . "]\n";
    echo "Featured route action: " . $featuredRoute->getActionName() . "\n";
} else {
    echo "Featured route: NOT FOUND\n";
}

if ($statusRoute) {
    echo "Status route found: " . $statusRoute->uri() . " [" . implode(',', $statusRoute->methods()) . "]\n";
    echo "Status route action: " . $statusRoute->getActionName() . "\n";
} else {
    echo "Status route: NOT FOUND\n";
}

if ($publishRoute) {
    echo "Publish route found: " . $publishRoute->uri() . " [" . implode(',', $publishRoute->methods()) . "]\n";
    echo "Publish route action: " . $publishRoute->getActionName() . "\n";
} else {
    echo "Publish route: NOT FOUND\n";
}
echo "\n";

// 7. Environment check
echo "7. ENVIRONMENT CHECK:\n";
echo "App Environment: " . app()->environment() . "\n";
echo "App Debug: " . (config('app.debug') ? 'true' : 'false') . "\n";
echo "Routes Cached: " . (app()->routesAreCached() ? 'true' : 'false') . "\n";
echo "Config Cached: " . (app()->configurationIsCached() ? 'true' : 'false') . "\n";

echo "\n=== DEBUG SCRIPT COMPLETE ===\n";
echo "\nNext Steps:\n";
echo "1. Run this script on both local and production environments\n";
echo "2. Compare the outputs to identify differences\n";
echo "3. Check the browser console for JavaScript errors\n";
echo "4. Monitor the Laravel logs for any errors during AJAX requests\n";
echo "5. Use the debug routes added to test the functionality:\n";
echo "   - /debug/featured-routes\n";
echo "   - /debug/test-featured-toggle/{course-slug}\n";
echo "   - PATCH /debug/test-ajax-featured/{course-slug}\n";
