# Featured Toggle Debug Guide

## Issue Summary
The featured/unfeatured toggle functionality works locally but fails silently on the production server. The buttons don't respond when clicked, but there are no visible JavaScript console errors.

## Key Differences Identified

### Working Publish Toggle vs Broken Featured Toggle

| Aspect | Publish Toggle (Working) | Featured Toggle (Broken) |
|--------|-------------------------|-------------------------|
| Route | `/instructor/course-builder/{course}/toggle-publish` | `/instructor/courses/{course}/toggle-featured` |
| Method | POST | PATCH |
| Controller | CourseBuilderController | CourseController |
| Route Group | course-builder prefix | courses resource |
| JavaScript Body | `{ status: newStatus }` | `{ featured: newFeatured }` |

## Debugging Tools Added

### 1. Enhanced JavaScript Logging
- **File**: `public/js/instructor/course-builder/course-builder-api.js`
- **Function**: `toggleFeaturedStatus()`
- **Added**: Comprehensive console logging, error handling, loading states

### 2. Enhanced Controller Logging
- **File**: `app/Http/Controllers/Instructor/CourseController.php`
- **Method**: `toggleFeatured()`
- **Added**: Request details, authorization checks, database operations logging

### 3. Debug Routes
- `/debug/featured-routes` - Route resolution and URL generation test
- `/debug/test-featured-toggle/{course}` - Course-specific toggle test
- `/debug/test-ajax-featured/{course}` - AJAX request test endpoint

### 4. Debug Script
- **File**: `debug_featured_toggle.php`
- **Purpose**: Comprehensive environment and functionality testing

### 5. Interactive Debug Page
- **URL**: `/debug/featured-toggle-test`
- **File**: `resources/views/debug/featured-toggle-test.blade.php`
- **Features**: Live testing interface with detailed logging

## Debugging Steps

### Step 1: Run Debug Script
```bash
php debug_featured_toggle.php
```
Run this on both local and production environments to compare outputs.

### Step 2: Check Debug Routes
Visit these URLs on your production server:
- `/debug/featured-routes`
- `/debug/test-featured-toggle/{course-slug}`

### Step 3: Use Interactive Debug Page
1. Visit `/debug/featured-toggle-test`
2. Select a course
3. Click "Test Featured Toggle"
4. Monitor the debug log for detailed information

### Step 4: Monitor Laravel Logs
Check `storage/logs/laravel.log` for detailed request/response information.

### Step 5: Browser Console
Open browser developer tools and monitor:
- Network tab for AJAX requests
- Console tab for JavaScript errors
- Response details for failed requests

## Potential Issues to Check

### 1. Route Caching
```bash
# Clear route cache
php artisan route:clear
php artisan route:cache
```

### 2. Model Binding
- Check if course resolution by slug works correctly
- Verify `getRouteKeyName()` returns 'slug'

### 3. Middleware Differences
- Compare middleware applied to different route groups
- Check instructor middleware authorization

### 4. Environment Differences
- Route caching status
- Debug mode settings
- Database connection issues

### 5. CSRF Token Issues
- Verify CSRF token is correctly included
- Check if token validation is working

## Expected Debug Output

### Successful Request
```
Featured Toggle Debug: {courseId: "course-slug", currentFeatured: false, ...}
Featured Toggle Response: {status: 200, ok: true, ...}
Raw response text: {"success":true,"message":"Course marked as featured successfully!","featured":true}
Parsed response data: {success: true, message: "...", featured: true}
Featured status updated successfully
```

### Failed Request
```
Featured Toggle Debug: {courseId: "course-slug", ...}
Featured Toggle Response: {status: 404, ok: false, ...}
Error toggling featured status: HTTP 404: Not Found
```

## Quick Fixes to Try

### 1. Change HTTP Method
Try changing from PATCH to POST in the JavaScript:
```javascript
method: 'POST' // instead of 'PATCH'
```

### 2. Use Form Submission (Like Status Toggle)
Replace AJAX with form submission similar to the working status toggle.

### 3. Move to Course Builder Route Group
Consider moving the featured toggle to the course-builder route group where publish toggle works.

## Log Monitoring Commands

### Real-time Log Monitoring
```bash
tail -f storage/logs/laravel.log | grep "Featured Toggle"
```

### Search for Errors
```bash
grep -i "featured toggle" storage/logs/laravel.log
grep -i "error" storage/logs/laravel.log | grep -i "featured"
```

## Next Steps After Debugging

1. Compare debug outputs between local and production
2. Identify the specific point of failure
3. Implement targeted fix based on findings
4. Test the fix using the debug tools
5. Remove debug code once issue is resolved

## Files Modified for Debugging

- `public/js/instructor/course-builder/course-builder-api.js` - Enhanced JavaScript logging
- `app/Http/Controllers/Instructor/CourseController.php` - Enhanced controller logging
- `routes/web.php` - Added debug routes
- `debug_featured_toggle.php` - Debug script
- `resources/views/debug/featured-toggle-test.blade.php` - Interactive debug page

Remember to remove or disable debug logging in production once the issue is resolved.
