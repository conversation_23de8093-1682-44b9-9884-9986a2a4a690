<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>JavaScript Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        button {
            background-color: #dc2626;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #b91c1c;
        }
        .log {
            background-color: #000;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .info { color: #3b82f6; }
    </style>
</head>
<body>
    <h1>JavaScript Test Page</h1>
    <p>This page tests basic JavaScript functionality and course builder components.</p>

    <div class="test-section">
        <h2>Basic JavaScript Test</h2>
        <button onclick="testBasicJS()">Test Basic JavaScript</button>
        <button onclick="testCSRFToken()">Test CSRF Token</button>
        <button onclick="testFetch()">Test Fetch API</button>
        <div id="basic-log" class="log"></div>
    </div>

    <div class="test-section">
        <h2>Course Builder JavaScript Test</h2>
        <p>This simulates the course builder environment:</p>
        <div data-course-id="test-course-slug">
            <button id="featured-toggle-btn" 
                    data-course-id="test-course-slug" 
                    data-current-featured="false">
                Test Featured Toggle
            </button>
        </div>
        <button onclick="testCourseBuilderJS()">Test Course Builder JS Loading</button>
        <button onclick="testFeaturedToggleFunction()">Test Featured Toggle Function</button>
        <div id="course-log" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info', logId = 'basic-log') {
            const logElement = document.getElementById(logId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.innerHTML += `<span class="${type}">${logEntry}</span>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function testBasicJS() {
            log('Testing basic JavaScript...', 'info');
            try {
                log('✓ JavaScript is working', 'success');
                log('✓ DOM manipulation working', 'success');
                log('✓ Event handlers working', 'success');
            } catch (error) {
                log(`✗ JavaScript error: ${error.message}`, 'error');
            }
        }

        function testCSRFToken() {
            log('Testing CSRF token...', 'info');
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                log(`✓ CSRF token found: ${csrfToken.getAttribute('content').substring(0, 10)}...`, 'success');
            } else {
                log('✗ CSRF token not found', 'error');
            }
        }

        function testFetch() {
            log('Testing Fetch API...', 'info');
            fetch('/debug/course-state/test-course-slug', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                log(`✓ Fetch response: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                return response.text();
            })
            .then(text => {
                log(`Response: ${text.substring(0, 100)}...`, 'info');
            })
            .catch(error => {
                log(`✗ Fetch error: ${error.message}`, 'error');
            });
        }

        function testCourseBuilderJS() {
            log('Testing course builder JavaScript...', 'info', 'course-log');
            
            // Test if course ID can be found
            const courseElement = document.querySelector('[data-course-id]');
            if (courseElement) {
                const courseId = courseElement.getAttribute('data-course-id');
                log(`✓ Course element found with ID: ${courseId}`, 'success', 'course-log');
            } else {
                log('✗ Course element not found', 'error', 'course-log');
            }

            // Test if featured toggle button exists
            const featuredBtn = document.getElementById('featured-toggle-btn');
            if (featuredBtn) {
                log('✓ Featured toggle button found', 'success', 'course-log');
                log(`Button data-course-id: ${featuredBtn.getAttribute('data-course-id')}`, 'info', 'course-log');
                log(`Button data-current-featured: ${featuredBtn.getAttribute('data-current-featured')}`, 'info', 'course-log');
            } else {
                log('✗ Featured toggle button not found', 'error', 'course-log');
            }
        }

        function testFeaturedToggleFunction() {
            log('Testing featured toggle function...', 'info', 'course-log');
            
            // Check if the function exists
            if (typeof toggleFeaturedStatus === 'function') {
                log('✓ toggleFeaturedStatus function exists', 'success', 'course-log');
                try {
                    // Don't actually call it, just test if it's callable
                    log('✓ Function is callable', 'success', 'course-log');
                } catch (error) {
                    log(`✗ Function error: ${error.message}`, 'error', 'course-log');
                }
            } else {
                log('✗ toggleFeaturedStatus function not found', 'error', 'course-log');
                log('Available functions:', 'info', 'course-log');
                log(`- typeof window.toggleFeaturedStatus: ${typeof window.toggleFeaturedStatus}`, 'info', 'course-log');
            }
        }

        // Test button click event
        document.getElementById('featured-toggle-btn')?.addEventListener('click', function(e) {
            e.preventDefault();
            log('Featured toggle button clicked!', 'success', 'course-log');
        });
    </script>

    <!-- Load course builder scripts to test -->
    <script src="{{ asset('js/instructor/course-builder/course-builder-utils.js') }}"></script>
    <script src="{{ asset('js/instructor/course-builder/course-builder-main.js') }}"></script>
    <script src="{{ asset('js/instructor/course-builder/course-builder-api.js') }}"></script>
</body>
</html>
