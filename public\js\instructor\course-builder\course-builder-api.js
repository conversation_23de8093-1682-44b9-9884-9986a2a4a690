/**
 * Course Builder API Module
 * Handles all API communication for saving data
 */

// Auto-save wrapper functions
function autoSaveCourse() {
    return saveCourse();
}

function autoSaveChapter(chapterId) {
    return saveChapter(chapterId);
}

function autoSaveLecture(lectureId) {
    return saveLecture(lectureId);
}

// Save functions
function saveCourse() {
    const form = document.getElementById('course-details-form');
    if (!form) {
        console.error('Course details form not found');
        showError('Form not found');
        return;
    }

    // Check if courseId is set
    if (!courseId) {
        console.error('Course ID not set');
        showError('Course ID missing');
        return;
    }

    // Check CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (!csrfToken) {
        console.error('CSRF token not found');
        showError('Security token missing');
        return;
    }

    console.log('Saving course with ID:', courseId);

    const formData = new FormData(form);

    // Log form data for debugging
    console.log('Form data being sent:');
    for (let [key, value] of formData.entries()) {
        console.log(key, value);
    }

    // Show saving status
    // showSaveStatus('saving', 'Saving course details...');

    fetch(`/instructor/course-builder/${courseId}/auto-save`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);

        if (data.success) {
            showSuccess('Course details saved successfully');
            updateCourseOverview(data.data);
        } else {
            console.error('Save failed:', data);
            showError(data.message || 'Failed to save course details');

            // Log validation errors if present
            if (data.errors) {
                console.error('Validation errors:', data.errors);
            }
        }
    })
    .catch(error => {
        console.error('Error saving course:', error);
        showError('Network error while saving course details: ' + error.message);
    });
}

function autoSaveCourse() {
    // Auto-save course details
    console.log('Auto-saving course...');
    saveCourse();
}

function updateCourseOverview(courseData) {
    // Update course title in overview
    const titleElements = document.querySelectorAll('.course-title');
    titleElements.forEach(element => {
        element.textContent = courseData.title;
    });

    // Update subtitle if exists
    if (courseData.subtitle) {
        const subtitleElements = document.querySelectorAll('.course-subtitle');
        subtitleElements.forEach(element => {
            element.textContent = courseData.subtitle;
        });
    }

    // Update price display
    const priceElements = document.querySelectorAll('.course-price');
    priceElements.forEach(element => {
        if (courseData.price > 0) {
            element.innerHTML = `<span class="text-2xl font-bold text-green-400">$${parseFloat(courseData.price).toFixed(2)}</span>`;
        } else {
            element.innerHTML = `<span class="text-2xl font-bold text-blue-400">FREE</span>`;
        }
    });
}

function saveChapter(chapterId) {
    const form = document.getElementById(`chapter-form-${chapterId}`);
    if (!form) return;

    const formData = new FormData(form);
    
    // Handle checkbox values explicitly - FormData only includes checked checkboxes
    // We need to explicitly set boolean values for unchecked checkboxes
    const publishedCheckbox = form.querySelector('input[name="is_published"]');
    
    if (publishedCheckbox) {
        formData.set('is_published', publishedCheckbox.checked ? '1' : '0');
    }
    
    // Convert learning objectives from textarea to array
    const learningObjectivesText = formData.get('learning_objectives');
    if (learningObjectivesText) {
        const objectives = learningObjectivesText.split('\n').filter(obj => obj.trim() !== '');
        formData.delete('learning_objectives');
        // Send as array elements instead of JSON string
        objectives.forEach((objective, index) => {
            formData.append(`learning_objectives[${index}]`, objective);
        });
    }

    fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/auto-save`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Chapter saved successfully');
            
            // Clear any persistent saving notifications
            const feedbackElement = document.getElementById(`immediate-feedback-${chapterId}-is_published`);
            if (feedbackElement) {
                setTimeout(() => {
                    feedbackElement.remove();
                }, 2000); // Remove after 2 seconds
            }
            
            // Update chapter title in sidebar
            const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"] .text-white`);
            if (chapterElement && data.data.title) {
                chapterElement.textContent = data.data.title;
            }
            
            // Real-time UI updates for Published and Free Preview status
            updateChapterStatusIndicators(chapterId, data.data);
            
            // Show real-time feedback for checkbox changes
            showChapterCheckboxStatusFeedback(chapterId, publishedCheckbox);
            
        } else {
            showError(data.message || 'Failed to save chapter');
        }
    })
    .catch(error => {
        console.error('Error saving chapter:', error);
        showError('Network error while saving chapter');
    });
}

function autoSaveChapter(chapterId) {
    // Auto-save chapter details
    console.log('Auto-saving chapter:', chapterId);
    saveChapter(chapterId);
}

function saveLecture(lectureId) {
    const form = document.getElementById(`lecture-form-${lectureId}`);
    if (!form) return;

    // Find the chapter ID for this lecture
    const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
    const chapterId = lectureElement?.closest('.chapter-lectures')?.id.replace('chapter-lectures-', '');

    if (!chapterId) {
        console.error('Could not find chapter ID for lecture:', lectureId);
        showError('Could not save lecture: chapter not found');
        return;
    }

    const formData = new FormData(form);
    
    // Handle checkbox values explicitly - FormData only includes checked checkboxes
    // We need to explicitly set boolean values for unchecked checkboxes
    const publishedCheckbox = form.querySelector('input[name="is_published"]');
    const freePreviewCheckbox = form.querySelector('input[name="is_free_preview"]');
    
    if (publishedCheckbox) {
        formData.set('is_published', publishedCheckbox.checked ? '1' : '0');
    }
    
    if (freePreviewCheckbox) {
        formData.set('is_free_preview', freePreviewCheckbox.checked ? '1' : '0');
    }

    fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}/auto-save`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Lecture saved successfully');
            
            // Update lecture title in sidebar
            const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"] .text-gray-300`);
            if (lectureElement && data.data.title) {
                lectureElement.textContent = data.data.title;
            }
            
            // Update lecture icon based on type
            const iconElement = document.querySelector(`[data-lecture-id="${lectureId}"] i`);
            if (iconElement && data.data.type) {
                iconElement.className = getLectureIcon(data.data.type);
            }
            
            // Real-time UI updates for Published and Free Preview status
            updateLectureStatusIndicators(lectureId, data.data);
            
            // Show real-time feedback for checkbox changes
            showCheckboxStatusFeedback(lectureId, publishedCheckbox, freePreviewCheckbox);
            
        } else {
            showError(data.message || 'Failed to save lecture');
        }
    })
    .catch(error => {
        console.error('Error saving lecture:', error);
        showError('Network error while saving lecture');
    });
}

function autoSaveLecture(lectureId) {
    // Auto-save lecture details
    console.log('Auto-saving lecture:', lectureId);
    saveLecture(lectureId);
}

function getLectureIcon(type) {
    const icons = {
        'video': 'fas fa-play-circle text-blue-500',
        'text': 'fas fa-file-text text-green-500',
        'quiz': 'fas fa-question-circle text-purple-500',
        'assignment': 'fas fa-tasks text-orange-500',
        'resource': 'fas fa-download text-gray-500'
    };
    return icons[type] || 'fas fa-file text-gray-500';
}

function togglePublishStatus() {
    const button = document.getElementById('publish-toggle-btn');
    if (!button) return;

    const currentStatus = button.getAttribute('data-current-status');
    const newStatus = currentStatus === 'published' ? 'draft' : 'published';

    fetch(`/instructor/course-builder/${courseId}/toggle-publish`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button appearance and text
            button.setAttribute('data-current-status', newStatus);
            
            if (newStatus === 'published') {
                button.className = 'px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors bg-green-600 hover:bg-green-700 text-white';
                button.innerHTML = '<i class="fas fa-eye mr-1 md:mr-2"></i><span class="hidden sm:inline">Published</span><span class="sm:hidden">Live</span>';
                showSuccess('Course published successfully');
            } else {
                button.className = 'px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors bg-red-600 hover:bg-red-700 text-white';
                button.innerHTML = '<i class="fas fa-eye-slash mr-1 md:mr-2"></i><span class="hidden sm:inline">Publish Course</span><span class="sm:hidden">Publish</span>';
                showSuccess('Course unpublished successfully');
            }
        } else {
            showError(data.message || 'Failed to update course status');
        }
    })
    .catch(error => {
        console.error('Error toggling publish status:', error);
        showError('Network error while updating course status');
    });
}

/**
 * Toggle course featured status
 */
function toggleFeaturedStatus() {
    const button = document.getElementById('featured-toggle-btn');
    if (!button) {
        console.error('Featured toggle button not found');
        return;
    }

    const currentFeatured = button.getAttribute('data-current-featured') === 'true';
    const newFeatured = !currentFeatured;

    // Debug logging
    console.log('Featured Toggle Debug:', {
        courseId: courseId,
        currentFeatured: currentFeatured,
        newFeatured: newFeatured,
        buttonData: {
            'data-course-id': button.getAttribute('data-course-id'),
            'data-current-featured': button.getAttribute('data-current-featured')
        },
        url: `/instructor/courses/${courseId}/toggle-featured`
    });

    // Disable button during request
    button.disabled = true;
    const originalContent = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1 md:mr-2"></i><span class="hidden sm:inline">Processing...</span><span class="sm:hidden">...</span>';

    fetch(`/instructor/courses/${courseId}/toggle-featured`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ featured: newFeatured })
    })
    .then(response => {
        console.log('Featured Toggle Response:', {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
            headers: Object.fromEntries(response.headers.entries())
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.text().then(text => {
            console.log('Raw response text:', text);
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Failed to parse JSON response:', e);
                console.log('Response was not JSON, text content:', text);
                throw new Error('Invalid JSON response from server');
            }
        });
    })
    .then(data => {
        console.log('Parsed response data:', data);

        if (data.success) {
            // Update button appearance and text
            button.setAttribute('data-current-featured', newFeatured.toString());

            if (newFeatured) {
                button.className = 'px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors bg-yellow-600 hover:bg-yellow-700 text-white';
                button.innerHTML = '<i class="fas fa-star mr-1 md:mr-2"></i><span class="hidden sm:inline">Featured</span><span class="sm:hidden">Featured</span>';
                showSuccess('Course marked as featured successfully');
            } else {
                button.className = 'px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors bg-gray-600 hover:bg-gray-700 text-white';
                button.innerHTML = '<i class="fas fa-star mr-1 md:mr-2"></i><span class="hidden sm:inline">Feature</span><span class="sm:hidden">Feature</span>';
                showSuccess('Course removed from featured successfully');
            }
            console.log('Featured status updated successfully');
        } else {
            console.error('Server returned success=false:', data);
            showError(data.message || 'Failed to update featured status');
        }
    })
    .catch(error => {
        console.error('Error toggling featured status:', error);
        showError(`Network error while updating featured status: ${error.message}`);

        // Restore original button content on error
        button.innerHTML = originalContent;
    })
    .finally(() => {
        // Re-enable button
        button.disabled = false;
    });
}

/**
 * Update lecture status indicators in real-time
 */
function updateLectureStatusIndicators(lectureId, lectureData) {
    // Update sidebar lecture preview badge
    updateSidebarPreviewBadge(lectureId, lectureData.is_free_preview);
    
    // Update published status visual indicator (if needed for future features)
    updateSidebarPublishedIndicator(lectureId, lectureData.is_published);
}

/**
 * Update chapter status indicators in real-time
 */
function updateChapterStatusIndicators(chapterId, chapterData) {
    // Update published status visual indicator
    updateSidebarChapterPublishedIndicator(chapterId, chapterData.is_published);
}



/**
 * Update the chapter published icon in the sidebar in real-time
 */
function updateSidebarChapterPublishedIndicator(chapterId, isPublished) {
    const sidebarChapter = document.querySelector(`[data-chapter-id="${chapterId}"]`);
    if (!sidebarChapter) return;
    
    // Find the icons container
    const iconsContainer = sidebarChapter.querySelector('.flex.items-center.space-x-1');
    if (!iconsContainer) return;
    
    // Find existing published icon
    const existingPublishedIcon = iconsContainer.querySelector('.published-icon');
    if (existingPublishedIcon) {
        // Update existing icon
        if (isPublished) {
            existingPublishedIcon.className = 'fas fa-eye text-green-500 text-xs published-icon';
            existingPublishedIcon.title = 'Published';
        } else {
            existingPublishedIcon.className = 'fas fa-eye-slash text-gray-500 text-xs published-icon';
            existingPublishedIcon.title = 'Unpublished';
        }
        
        // Add animation effect
        existingPublishedIcon.style.transform = 'scale(1.2)';
        existingPublishedIcon.style.transition = 'all 0.2s ease';
        
        setTimeout(() => {
            existingPublishedIcon.style.transform = 'scale(1)';
        }, 200);
    }
    
    // Update title element opacity based on published status
    const titleElement = sidebarChapter.querySelector('.text-white');
    if (titleElement) {
        titleElement.style.opacity = isPublished ? '1' : '0.6';
    }
}

/**
 * Update the preview icon in the sidebar in real-time
 */
function updateSidebarPreviewBadge(lectureId, isFreePreview) {
    const sidebarLecture = document.querySelector(`[data-lecture-id="${lectureId}"]`);
    if (!sidebarLecture) return;
    
    // Find the icons container
    const iconsContainer = sidebarLecture.querySelector('.flex.items-center.space-x-1');
    if (!iconsContainer) return;
    
    // Remove existing preview icon
    const existingPreviewIcon = iconsContainer.querySelector('.preview-icon');
    if (existingPreviewIcon) {
        existingPreviewIcon.remove();
    }
    
    // Add new preview icon if needed
    if (isFreePreview) {
        const previewIcon = document.createElement('i');
        previewIcon.className = 'fas fa-unlock text-blue-500 text-xs preview-icon';
        previewIcon.title = 'Free Preview';
        previewIcon.style.opacity = '0';
        previewIcon.style.transform = 'scale(0.8)';
        
        iconsContainer.appendChild(previewIcon);
        
        // Animate the icon appearance
        setTimeout(() => {
            previewIcon.style.transition = 'all 0.3s ease';
            previewIcon.style.opacity = '1';
            previewIcon.style.transform = 'scale(1)';
        }, 10);
    }
}

/**
 * Update published status icon in sidebar in real-time
 */
function updateSidebarPublishedIndicator(lectureId, isPublished) {
    const sidebarLecture = document.querySelector(`[data-lecture-id="${lectureId}"]`);
    if (!sidebarLecture) return;
    
    // Find the icons container
    const iconsContainer = sidebarLecture.querySelector('.flex.items-center.space-x-1');
    if (!iconsContainer) return;
    
    // Find existing published icon
    const existingPublishedIcon = iconsContainer.querySelector('.published-icon');
    if (existingPublishedIcon) {
        // Update existing icon
        if (isPublished) {
            existingPublishedIcon.className = 'fas fa-eye text-green-500 text-xs published-icon';
            existingPublishedIcon.title = 'Published';
        } else {
            existingPublishedIcon.className = 'fas fa-eye-slash text-gray-500 text-xs published-icon';
            existingPublishedIcon.title = 'Unpublished';
        }
        
        // Add animation effect
        existingPublishedIcon.style.transform = 'scale(1.2)';
        setTimeout(() => {
            existingPublishedIcon.style.transition = 'all 0.2s ease';
            existingPublishedIcon.style.transform = 'scale(1)';
        }, 100);
    }
    
    // Also update title opacity based on published status
    const titleElement = sidebarLecture.querySelector('.text-gray-300, .text-gray-500');
    if (titleElement) {
        if (isPublished) {
            titleElement.classList.remove('opacity-50', 'text-gray-500');
            titleElement.classList.add('text-gray-300');
        } else {
            titleElement.classList.add('opacity-50', 'text-gray-500');
            titleElement.classList.remove('text-gray-300');
        }
    }
}

/**
 * Show real-time feedback for checkbox status changes
 */
function showCheckboxStatusFeedback(lectureId, publishedCheckbox, freePreviewCheckbox) {
    // Create or update status feedback near the checkboxes
    let feedbackContainer = document.getElementById(`checkbox-feedback-${lectureId}`);
    
    if (!feedbackContainer) {
        feedbackContainer = document.createElement('div');
        feedbackContainer.id = `checkbox-feedback-${lectureId}`;
        feedbackContainer.className = 'mt-2 text-sm';
        
        // Insert after the checkboxes
        const form = document.getElementById(`lecture-form-${lectureId}`);
        const checkboxContainer = form?.querySelector('.checkbox-container') || 
                                form?.querySelector('[name="is_published"]')?.closest('.flex') ||
                                form?.querySelector('[name="is_free_preview"]')?.closest('.flex');
        
        if (checkboxContainer) {
            checkboxContainer.parentNode.insertBefore(feedbackContainer, checkboxContainer.nextSibling);
        }
    }
    
    // Clear existing content
    feedbackContainer.innerHTML = '';
    
    // Add status messages with animations
    const statusMessages = [];
    
    if (publishedCheckbox) {
        const isChecked = publishedCheckbox.checked;
        const message = document.createElement('div');
        message.className = `flex items-center space-x-2 p-2 rounded-lg transition-all duration-300 ${
            isChecked ? 'bg-green-900/30 text-green-400 border border-green-600/30' : 'bg-gray-800/30 text-gray-400 border border-gray-600/30'
        }`;
        message.innerHTML = `
            <i class="fas ${isChecked ? 'fa-eye' : 'fa-eye-slash'} text-sm"></i>
            <span class="font-medium">${isChecked ? 'Published' : 'Draft'}</span>
            <span class="text-xs opacity-75">- ${isChecked ? 'Visible to students' : 'Hidden from students'}</span>
        `;
        statusMessages.push(message);
    }
    
    if (freePreviewCheckbox) {
        const isChecked = freePreviewCheckbox.checked;
        const message = document.createElement('div');
        message.className = `flex items-center space-x-2 p-2 rounded-lg transition-all duration-300 ${
            isChecked ? 'bg-blue-900/30 text-blue-400 border border-blue-600/30' : 'bg-gray-800/30 text-gray-400 border border-gray-600/30'
        }`;
        message.innerHTML = `
            <i class="fas ${isChecked ? 'fa-unlock' : 'fa-lock'} text-sm"></i>
            <span class="font-medium">${isChecked ? 'Free Preview' : 'Paid Content'}</span>
            <span class="text-xs opacity-75">- ${isChecked ? 'Accessible without enrollment' : 'Requires course enrollment'}</span>
        `;
        statusMessages.push(message);
    }
    
    // Add messages to container with staggered animation
    statusMessages.forEach((message, index) => {
        message.style.opacity = '0';
        message.style.transform = 'translateY(10px)';
        feedbackContainer.appendChild(message);
        
        setTimeout(() => {
            message.style.opacity = '1';
            message.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Auto-hide feedback after 5 seconds
    setTimeout(() => {
        if (feedbackContainer) {
            feedbackContainer.style.opacity = '0';
            feedbackContainer.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (feedbackContainer.parentNode) {
                    feedbackContainer.parentNode.removeChild(feedbackContainer);
                }
            }, 300);
        }
    }, 5000);
}

/**
 * Show real-time feedback for chapter checkbox status changes
 */
function showChapterCheckboxStatusFeedback(chapterId, publishedCheckbox) {
    // Create or update status feedback near the checkboxes
    let feedbackContainer = document.getElementById(`checkbox-feedback-${chapterId}`);
    
    if (!feedbackContainer) {
        feedbackContainer = document.createElement('div');
        feedbackContainer.id = `checkbox-feedback-${chapterId}`;
        feedbackContainer.className = 'mt-2 text-sm';
        
        // Insert after the checkboxes
        const form = document.getElementById(`chapter-form-${chapterId}`);
        const checkboxContainer = form?.querySelector('.checkbox-container') || 
                                form?.querySelector('[name="is_published"]')?.closest('.flex');
        
        if (checkboxContainer) {
            checkboxContainer.parentNode.insertBefore(feedbackContainer, checkboxContainer.nextSibling);
        }
    }
    
    // Clear existing content
    feedbackContainer.innerHTML = '';
    
    // Add status messages with animations
    const statusMessages = [];
    
    if (publishedCheckbox) {
        const isChecked = publishedCheckbox.checked;
        const message = document.createElement('div');
        message.className = `flex items-center space-x-2 p-2 rounded-lg transition-all duration-300 ${
            isChecked ? 'bg-green-900/30 text-green-400 border border-green-600/30' : 'bg-gray-800/30 text-gray-400 border border-gray-600/30'
        }`;
        message.innerHTML = `
            <i class="fas ${isChecked ? 'fa-eye' : 'fa-eye-slash'} text-sm"></i>
            <span class="font-medium">${isChecked ? 'Published' : 'Draft'}</span>
            <span class="text-xs opacity-75">- ${isChecked ? 'Visible to students' : 'Hidden from students'}</span>
        `;
        statusMessages.push(message);
    }
    
    // Add messages to container with staggered animation
    statusMessages.forEach((message, index) => {
        message.style.opacity = '0';
        message.style.transform = 'translateY(10px)';
        feedbackContainer.appendChild(message);
        
        setTimeout(() => {
            message.style.opacity = '1';
            message.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Auto-hide feedback after 5 seconds
    setTimeout(() => {
        if (feedbackContainer) {
            feedbackContainer.style.opacity = '0';
            feedbackContainer.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (feedbackContainer.parentNode) {
                    feedbackContainer.parentNode.removeChild(feedbackContainer);
                }
            }, 300);
        }
    }, 5000);
}

// Expose functions to global scope
window.saveCourse = saveCourse;
window.saveChapter = saveChapter;
window.saveLecture = saveLecture;
window.togglePublishStatus = togglePublishStatus;
