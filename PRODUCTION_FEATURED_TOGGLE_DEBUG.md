# Production Featured Toggle Debug Guide

## Problem Summary
The featured toggle button in the course builder interface is not responding to clicks on the production server, despite working locally and the debug routes working correctly.

## Changes Made

### 1. Enhanced Course Builder Button
**File**: `resources/views/instructor/course-builder/show.blade.php`

**Changes**:
- Changed `data-course-id` to `data-course-slug` for consistency
- Added `onclick="handleFeaturedToggle(this)"` for direct event handling
- Added inline JavaScript function that works independently of the complex course builder system

### 2. Enhanced JavaScript Debugging
**File**: `public/js/instructor/course-builder/course-builder-main.js`

**Changes**:
- Added console logging for course ID initialization
- Enhanced featured toggle button event binding with detailed logging
- Added validation checks for button existence

### 3. Enhanced API Function
**File**: `public/js/instructor/course-builder/course-builder-api.js`

**Changes**:
- Added comprehensive validation (courseId, CSRF token, button existence)
- Enhanced error handling and logging
- Better debugging output

### 4. New Debug Tools

**New Routes**:
- `/debug/js-test` - JavaScript functionality test page
- `/debug/course-state/{course}` - JSON endpoint for course state verification

**New Files**:
- `resources/views/debug/js-test.blade.php` - Comprehensive JavaScript testing

## Testing Steps

### Step 1: Test JavaScript Loading
1. Visit `/debug/js-test` on production
2. Click all test buttons
3. Check console for any JavaScript errors
4. Verify all course builder scripts are loading

### Step 2: Test Course Builder Page
1. Go to any course builder page (`/instructor/course-builder/{course-slug}`)
2. Open browser developer tools (F12)
3. Go to Console tab
4. Look for these messages:
   - "Course Builder: Course ID initialized: [slug]"
   - "Course Builder: Featured toggle button found, attaching event listener"

### Step 3: Test Featured Toggle Button
1. Click the featured toggle button
2. Check console for:
   - "Course Builder: Featured toggle button clicked"
   - "Featured toggle clicked"
   - "Featured toggle data: {...}"
   - Response status and data

### Step 4: Verify Network Requests
1. Go to Network tab in developer tools
2. Click featured toggle button
3. Look for PATCH request to `/instructor/courses/{slug}/toggle-featured`
4. Check request headers include:
   - `X-CSRF-TOKEN`
   - `Content-Type: application/json`
   - `X-Requested-With: XMLHttpRequest`

## Troubleshooting

### If Button Still Not Responding

1. **Check Console Errors**:
   ```javascript
   // Look for these in console:
   - "Course Builder: Featured toggle button not found"
   - "Course ID not available"
   - "CSRF token not found"
   ```

2. **Manual Test in Console**:
   ```javascript
   // Test button exists
   console.log(document.getElementById('featured-toggle-btn'));
   
   // Test course slug
   console.log(document.querySelector('[data-course-slug]'));
   
   // Test CSRF token
   console.log(document.querySelector('meta[name="csrf-token"]'));
   
   // Manually trigger function
   handleFeaturedToggle(document.getElementById('featured-toggle-btn'));
   ```

3. **Check File Loading**:
   ```javascript
   // Check if functions exist
   console.log(typeof handleFeaturedToggle);
   console.log(typeof toggleFeaturedStatus);
   ```

### Common Issues

1. **JavaScript Files Not Loading**:
   - Check network tab for 404 errors on JS files
   - Verify asset paths are correct
   - Check if Laravel Mix/Vite is properly configured

2. **CSRF Token Issues**:
   - Verify `<meta name="csrf-token" content="{{ csrf_token() }}">` exists
   - Check if token is valid and not expired

3. **Route Issues**:
   - Verify route exists: `php artisan route:list | grep toggle-featured`
   - Check middleware and permissions

4. **Caching Issues**:
   - Clear browser cache
   - Clear Laravel cache: `php artisan cache:clear`
   - Clear route cache: `php artisan route:clear`

## Fallback Solution

If the main button still doesn't work, the inline `handleFeaturedToggle()` function should work independently. It:

1. Uses direct `onclick` event (not dependent on complex event binding)
2. Has its own validation and error handling
3. Uses the same AJAX pattern as the working debug version
4. Provides detailed console logging for debugging

## Next Steps

1. Test on production using the steps above
2. Report any console errors or network issues
3. If still not working, we can:
   - Add more debugging
   - Create a completely separate featured toggle implementation
   - Investigate server-side differences between local and production

## Success Indicators

The featured toggle is working correctly when:
- ✅ Button responds to clicks (shows loading state)
- ✅ AJAX request is sent (visible in Network tab)
- ✅ Server returns HTTP 200 with `{"success":true}`
- ✅ Button appearance updates (yellow for featured, gray for not featured)
- ✅ No JavaScript console errors
