<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Featured Toggle Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        button {
            background-color: #dc2626;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #b91c1c;
        }
        button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
        .success {
            color: #10b981;
        }
        .error {
            color: #ef4444;
        }
        .info {
            color: #3b82f6;
        }
        #log {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Featured Toggle Debug Test</h1>
    
    @if($courses->count() > 0)
        <div class="test-section">
            <h2>Course Selection</h2>
            <select id="course-select">
                @foreach($courses as $course)
                    <option value="{{ $course->slug }}" data-id="{{ $course->id }}" data-featured="{{ $course->featured ? 'true' : 'false' }}">
                        {{ $course->title }} (Featured: {{ $course->featured ? 'Yes' : 'No' }})
                    </option>
                @endforeach
            </select>
        </div>

        <div class="test-section">
            <h2>Test Actions</h2>
            <button onclick="testFeaturedToggle()">Test Featured Toggle</button>
            <button onclick="testStatusToggle()">Test Status Toggle (Working)</button>
            <button onclick="testPublishToggle()">Test Publish Toggle (Working)</button>
            <button onclick="testDebugAjax()">Test Debug AJAX</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h2>Current Course Info</h2>
            <div id="course-info">
                <p>Select a course to see details</p>
            </div>
        </div>

        <div class="test-section">
            <h2>Debug Log</h2>
            <div id="log"></div>
        </div>
    @else
        <div class="test-section">
            <h2 class="error">No Courses Found</h2>
            <p>Please create some courses first to test the featured toggle functionality.</p>
        </div>
    @endif

    <script>
        let currentCourse = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateCourseInfo() {
            const select = document.getElementById('course-select');
            const option = select.options[select.selectedIndex];
            
            if (option) {
                currentCourse = {
                    slug: option.value,
                    id: option.dataset.id,
                    featured: option.dataset.featured === 'true',
                    title: option.text
                };

                document.getElementById('course-info').innerHTML = `
                    <p><strong>Title:</strong> ${option.text.split(' (Featured:')[0]}</p>
                    <p><strong>Slug:</strong> ${currentCourse.slug}</p>
                    <p><strong>ID:</strong> ${currentCourse.id}</p>
                    <p><strong>Featured:</strong> ${currentCourse.featured ? 'Yes' : 'No'}</p>
                `;

                log(`Selected course: ${currentCourse.slug} (Featured: ${currentCourse.featured})`);
            }
        }

        function testFeaturedToggle() {
            if (!currentCourse) {
                log('Please select a course first', 'error');
                return;
            }

            log(`Testing featured toggle for course: ${currentCourse.slug}`);
            
            const url = `/instructor/courses/${currentCourse.slug}/toggle-featured`;
            log(`URL: ${url}`);

            fetch(url, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ featured: !currentCourse.featured })
            })
            .then(response => {
                log(`Response status: ${response.status} ${response.statusText}`);
                log(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);
                
                return response.text().then(text => {
                    log(`Raw response: ${text}`);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error(`Invalid JSON: ${text}`);
                    }
                });
            })
            .then(data => {
                log(`Parsed response: ${JSON.stringify(data)}`, 'success');
                if (data.success) {
                    currentCourse.featured = !currentCourse.featured;
                    updateCourseInfo();
                }
            })
            .catch(error => {
                log(`Error: ${error.message}`, 'error');
            });
        }

        function testStatusToggle() {
            if (!currentCourse) {
                log('Please select a course first', 'error');
                return;
            }

            log(`Testing status toggle for course: ${currentCourse.slug}`);
            
            const url = `/instructor/courses/${currentCourse.slug}/toggle-status`;
            log(`URL: ${url}`);

            // This uses form submission like the working implementation
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            form.innerHTML = `
                <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                <input type="hidden" name="_method" value="PATCH">
            `;
            document.body.appendChild(form);
            
            log('Submitting form for status toggle...', 'info');
            form.submit();
        }

        function testPublishToggle() {
            if (!currentCourse) {
                log('Please select a course first', 'error');
                return;
            }

            log(`Testing publish toggle for course: ${currentCourse.slug}`);
            
            const url = `/instructor/course-builder/${currentCourse.slug}/toggle-publish`;
            log(`URL: ${url}`);

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                log(`Response status: ${response.status} ${response.statusText}`);
                return response.json();
            })
            .then(data => {
                log(`Publish toggle response: ${JSON.stringify(data)}`, 'success');
            })
            .catch(error => {
                log(`Publish toggle error: ${error.message}`, 'error');
            });
        }

        function testDebugAjax() {
            if (!currentCourse) {
                log('Please select a course first', 'error');
                return;
            }

            log(`Testing debug AJAX for course: ${currentCourse.slug}`);
            
            const url = `/debug/test-ajax-featured/${currentCourse.slug}`;
            log(`URL: ${url}`);

            fetch(url, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ test: true })
            })
            .then(response => {
                log(`Debug response status: ${response.status} ${response.statusText}`);
                return response.json();
            })
            .then(data => {
                log(`Debug response: ${JSON.stringify(data)}`, 'success');
            })
            .catch(error => {
                log(`Debug error: ${error.message}`, 'error');
            });
        }

        // Initialize
        document.getElementById('course-select').addEventListener('change', updateCourseInfo);
        updateCourseInfo(); // Set initial course
        log('Featured Toggle Debug Test initialized');
    </script>
</body>
</html>
